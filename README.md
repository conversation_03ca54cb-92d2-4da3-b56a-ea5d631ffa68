create branch example:
```text
📦 main
├──  story/login
├──  story/payment
├──  story/user-center
└──  story/login-bug
```

评论 + 点赞字段   BACKEND 判断是否点赞

增加是否达人字段？？ BACKEND 添加判断

create table article
(
    article_id   int auto_increment
        primary key,
    creator_id   int                                                                not null,
    title        varchar(255)                                                       not null,
    address      text                                                               null,
    content      text                                                               null,
    image        varchar(255)                                                       null,
    video        varchar(255)                                                       null,
    likes_num    int                                      default 0                 null,
    comments_num int                                      default 0                 null,
    create_time  timestamp                                default CURRENT_TIMESTAMP null,
    update_time  timestamp                                default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP,
    is_deleted   tinyint(1)                               default 0                 null,
    status       enum ('pending', 'approved', 'rejected') default 'pending'         not null
    abstract_content    text                                                        null
);

create table article_review
(
    review_id     int auto_increment
        primary key,
    article_id    int                                                                not null,
    reviewer_id   int                                                                not null,
    review_status enum ('pending', 'approved', 'rejected') default 'pending'         null,
    create_time   timestamp                                default CURRENT_TIMESTAMP null
);

create table article_tag
(
    id         int auto_increment
        primary key,
    article_id int not null,
    tag_id     int not null
);

create table comment
(
    comment_id     int auto_increment
        primary key,
    commentator_id int                                 not null,
    article_id     int                                 not null,
    content        text                                not null,
    likes_num      int       default 0                 null,
    create_time    timestamp default CURRENT_TIMESTAMP null
);

create table comment_likes
(
    id         int auto_increment
        primary key,
    user_id    int not null,
    comment_id int not null
);

create table likes
(
    id         int auto_increment
        primary key,
    user_id    int not null,
    article_id int not null
);

create table reply
(
    reply_id    int auto_increment
        primary key,
    comment_id  int                                 not null,
    replier_id  int                                 not null,
    content     text                                not null,
    create_time timestamp default CURRENT_TIMESTAMP null
);

create table tag
(
    tag_id   int auto_increment
        primary key,
    tag_name varchar(50) not null,
    constraint tag_name
        unique (tag_name)
);

create table user
(
    user_id     int auto_increment
        primary key,
    user_name   varchar(50)                                                not null,
    password    varchar(255)                                               not null,
    motto       text                                                       null,
    role        enum ('admin', 'author', 'user') default 'user'            null,
    create_time timestamp                        default CURRENT_TIMESTAMP null,
    point       int                              default 0                 not null,
    constraint user_name
        unique (user_name)
);

CREATE TABLE plan(
     plan_id INT AUTO_INCREMENT PRIMARY KEY,
     user_id INT NOT NULL,
     plan_content JSON NOT NULL,
     create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);


-- ========== 1. 用户表 ==========
INSERT INTO user (user_name, password, motto, role, point, create_time) VALUES
                                                                            ('admin01', 'hashed_admin_pwd_01', '管理世界，畅游天下', 'admin', 0, '2025-04-01 10:00:00'),
                                                                            ('author_alex', 'hashed_alex_pwd', '用脚步丈量地球', 'author', 0, '2025-04-02 09:30:00'),
                                                                            ('author_maya', 'hashed_maya_pwd', '镜头里的远方', 'author', 0, '2025-04-03 11:15:00'),
                                                                            ('user_lily', 'hashed_lily_pwd', '爱旅行，爱生活', 'user', 0, '2025-04-04 14:20:00'),
                                                                            ('user_tom', 'hashed_tom_pwd', '背包客的日常', 'user', 0, '2025-04-05 16:45:00'),
                                                                            ('user_sara', 'hashed_sara_pwd', '美食与风景不可辜负', 'user', 0, '2025-04-06 08:10:00');

-- ========== 2. 文章表（珠海小众景点） ==========
INSERT INTO article (creator_id, title, address, content, image, video, likes_num, comments_num, create_time, update_time, is_deleted, status, abstract_content) VALUES
(2, '淇澳岛白石街的隐秘红树林步道', '珠海·淇澳岛白石街尽头', '避开喧嚣的游客，从白石街那些充满故事的老屋后悄然拐入一条不起眼的小径，您将发现一个被本地人珍藏的生态秘境——一条蜿蜒约2公里的红树林栈道。这条栈道巧妙地融入了原始的湿地环境，让您能近距离观察这片独特的生态系统。当潮水退去，滩涂上便成了小生命的乐园，机灵的招潮蟹挥舞着大螯匆匆爬行，弹涂鱼则在泥泞中灵巧地跳跃，为静谧的红树林增添无限生机。这里不仅是观鸟爱好者的天堂，沿着栈道漫步，头顶是婆娑的绿荫，脚下是生机勃勃的湿地，凉风习习，非常适合悠闲地骑行或散步。记得自备垃圾袋，共同守护这片优美的自然景观。游玩之余，还可以顺道探访附近的古炮台或苏兆征故居，感受淇澳岛深厚的人文底蕴。', 'images/qiao_red_mangrove.jpg', NULL, 3, 2, '2025-04-07 10:00:00', '2025-04-08 15:30:00', 0, 'approved', '本地人才知道的生态秘境，免费+人少+适合亲子'),
(3, '斗门古村接霞庄的黄昏祠堂', '珠海·斗门区南门村接霞庄', '在珠海斗门区南门村，藏着一座拥有两百多年历史的岭南古村——接霞庄。它曾是富甲一方的赵氏庄园，又名新围，因东接霞山而得名。步入庄内，踏着被岁月磨得光滑的青石板路，两旁是保存完好的清代青砖灰瓦建筑，庄外更有护庄河静静环绕，东、西、北三面被荷花池与翠竹环抱，景色秀美静谧。最动人的时刻莫过于黄昏，当夕阳的余晖洒落，古老的赵氏祠堂倒映在半月形的池塘中，光影交错，如诗如画。此时，村中或许只有一位阿婆坐在门口安静地择菜，时光仿佛在此刻凝固，为您呈现出一幅比会同古村更为原生态、未经雕琢的岭南水乡画卷。导航至“接霞庄牌坊”后，步行入内，更能体会这份宁静与古朴。', 'images/jiexia_village.jpg', NULL, 4, 3, '2025-04-08 12:00:00', '2025-04-09 18:20:00', 0, 'approved', '比会同古村更原生态，导航“接霞庄牌坊”步行入内'),
(2, '金鼎山谷里的无名瀑布与野溪', '珠海·金鼎镇山谷（近永丰村）', '在珠海金鼎镇永丰村附近的山谷深处，隐藏着一处需要一点探索精神才能抵达的自然奇观。跟随一条清澈的溪流向上溯源，徒步约30分钟，穿过一片郁郁葱葱的荔枝林，眼前豁然开朗——一道约5米高的瀑布从嶙峋的石缝中倾泻而下，水声潺潺，带来沁人心脾的清凉。这是一条充满野趣的徒步路线，安全是首要前提，强烈建议穿着专业的防滑鞋。雨后的水量最为丰沛，瀑布气势磅礴，是观赏的最佳时机。虽然为了安全禁止游泳，但您可以在溪边安全的区域戏水，享受山野间的清凉乐趣。这是一次远离城市喧嚣，亲近自然的绝佳体验。', NULL, 'videos/zhuhai_waterfall.mp4', 2, 1, '2025-04-09 09:00:00', '2025-04-10 11:00:00', 0, 'approved', '需穿防滑鞋，雨后水量最大，禁止游泳但可戏水'),
(3, '唐家湾老街后山的碉楼与茶果作坊', '珠海·唐家湾山房路后山', '沿着唐家湾山房路这条充满历史韵味的街道漫步，您会发现一条通往后山的石阶小径。拾级而上约200级，两座静默矗立的民国碉楼便出现在山腰，它们曾是古村的守护者，如今登顶可免费眺望辽阔的海景，感受历史的沧桑。更令人惊喜的是，旁边仍有阿婆坚守着传统，手工制作着地道的唐家湾茶果。这种小吃已有数百年历史，原本是节令和祭祀的食品，如今已成为日常美味。热腾腾的“萝卜糕”和“芋头糕”香气扑鼻，只需5元一个，是体验当地非遗文化和市井烟火气的绝佳方式。记得带上现金，寻味于山房路附近，感受唐家古镇独有的文化韵味。', 'images/tangjia_tea_cake.jpg', NULL, 3, 2, '2025-04-10 14:00:00', '2025-04-11 09:45:00', 0, 'approved', '5元一个茶果，现金支付，碉楼可免费登顶看海'),
(2, '横琴杧洲湿地的观鸟木屋', '珠海·横琴杧洲湿地公园西北角', '位于横琴杧洲湿地公园西北角的这片区域，藏着一个连地图都未曾标注的观鸟秘境。您需要向公园保安询问，才能找到通往湖心那座废弃木屋的小径。这座看似不起眼的木屋，实则是观察湿地鸟类的绝佳据点。尤其是在清晨6:30至8:00之间，当第一缕阳光洒向湿地，成群的白鹭展翅飞翔，景象蔚为壮观。这里是名副其实的“雀鸟天堂”，公园的设计旨在方便鸟类栖息，同时也为观鸟者提供了良好的观察条件。为了不惊扰这些自然的精灵，请务必带上望远镜，并保持绝对的安静，用镜头和心灵去捕捉这和谐共生的自然之美。', 'images/mangzhou_bird.jpg', NULL, 1, 1, '2025-04-11 16:30:00', '2025-04-12 13:20:00', 0, 'approved', '带望远镜！禁止喧哗，最佳时间6:30-8:00'),
(3, '湾仔沙旧货市场的怀旧早餐摊', '珠海·湾仔沙旧货市场巷内', '在湾仔沙旧货市场熙熙攘攘的巷子深处，藏着一份延续了半个世纪的温暖味道。市场清晨7点开门，巷尾一位阿伯支起的“猪肠粉+芝麻糊”早餐摊，是许多老珠海人记忆中的味道。他坚持使用50年不变的老配方，只为那份熟悉而醇厚的滋味。这份早餐简单却充满诚意，仅需3元一碗，但只收现金，且每日只营业到上午9点。摊位没有显眼的招牌，只需认准那顶蓝色的遮阳棚和几位忠实排队的老顾客（通常是阿婆们），便能找到。这不仅是一顿早餐，更是一场穿越时光的怀旧之旅，品味的是城市最本真的烟火气。', NULL, NULL, 2, 1, '2025-04-12 11:00:00', '2025-04-13 17:10:00', 0, 'approved', '现金3元/碗，无招牌，认准蓝布棚+排队阿婆'),
(2, '高栏港风车山下的废弃盐田', '珠海·高栏港风车山路尽头', '导航至高栏港风车山路的尽头，到达观景台后，继续向深处徒步约15分钟，一片被遗忘的废弃盐田便在巨大的风车脚下铺展开来。这片曾经繁忙的生产场地如今归于沉寂，散落着昔日用于平整盐池的石磙。然而，大自然赋予了它新的生命与色彩。尤其在雨后，盐田中残留的卤水在特定条件下会泛起梦幻般的粉紫色，与蓝天、白云、巨大的白色风车构成一幅超现实的画卷，是摄影爱好者不可错过的独特景致。这里几乎没有遮阴，阳光强烈，请务必做好防晒措施，带着探索和发现美的眼睛，来捕捉这片工业遗迹与自然奇观交融的壮丽景象。', 'images/gaolan_salt.jpg', NULL, 0, 0, '2025-04-13 15:00:00', '2025-04-13 15:00:00', 0, 'approved', '雨后盐田变粉红，适合摄影，注意防晒无遮阴');
-- ========== 4. 标签表 + 文章标签关联 ==========
INSERT INTO tag (tag_name) VALUES
                               ('珠海小众'), ('红树林'), ('古村落'), ('斗门'), ('瀑布'), ('唐家湾'),
                               ('碉楼'), ('茶果'), ('观鸟'), ('横琴'), ('旧货市场'), ('怀旧早餐'),
                               ('盐田'), ('高栏港'), ('Pending'), ('本地人才知道');

INSERT INTO article_tag (article_id, tag_id) VALUES
                                                 (1, 1), (1, 2), (1, 16),
                                                 (2, 1), (2, 3), (2, 4), (2, 16),
                                                 (3, 1), (3, 5), (3, 16),
                                                 (4, 1), (4, 6), (4, 7), (4, 8), (4, 16),
                                                 (5, 1), (5, 9), (5, 10), (5, 16),
                                                 (6, 1), (6, 11), (6, 12), (6, 16),
                                                 (7, 1), (7, 13), (7, 14), (7, 15), (7, 16);

-- ========== 5. 评论表 ==========
INSERT INTO comment (commentator_id, article_id, content, likes_num, create_time) VALUES
                                                                                      (4, 1, '红树林步道入口真的难找！楼主能发个定位截图吗？', 1, '2025-04-08 16:00:00'),
                                                                                      (5, 1, '上周刚去！退潮时还能捡到小贝壳，带孩子超合适', 0, '2025-04-08 17:10:00'),
                                                                                      (6, 2, '接霞庄祠堂前的池塘，雨后倒影绝了！阿婆还送了我一个刚蒸的芋头糕', 2, '2025-04-09 19:00:00'),
                                                                                      (4, 2, '导航到“赵氏祖祠”更准！村里还有家无名肠粉，5元管饱', 1, '2025-04-09 20:30:00'),
                                                                                      (5, 2, '建议穿防蚊裤！傍晚蚊子超多，但夕阳值了', 0, '2025-04-10 08:45:00'),
                                                                                      (6, 3, '瀑布下面水超凉！我们带了西瓜泡着吃，爽翻', 1, '2025-04-10 12:00:00'),
                                                                                      (4, 4, '茶果阿婆只收现金！碉楼二楼窗户看出去是情侣北路，人少景美', 2, '2025-04-11 10:30:00'),
                                                                                      (5, 4, '碉楼楼梯陡，带老人要小心。茶果下午3点就卖完', 0, '2025-04-11 11:15:00'),
                                                                                      (6, 5, '木屋要早去！8点后观鸟团就来了。我拍到翠鸟捕鱼！', 1, '2025-04-12 14:00:00'),
                                                                                      (5, 6, '阿伯的芝麻糊是石磨的！他说年轻人都不做这个了...', 1, '2025-04-13 18:00:00');

-- ========== 6. 回复表 ==========
INSERT INTO reply (comment_id, replier_id, content, create_time) VALUES
                                                                     (1, 2, '入口在白石街“海哥海鲜档”右转，看到榕树就对了！', '2025-04-08 16:30:00'),
                                                                     (3, 3, '阿婆姓陈，说赵氏是她夫家~ 芋头糕周三周五才有！', '2025-04-09 19:45:00'),
                                                                     (7, 3, '碉楼免费但要登记身份证！阿婆茶果只开到中午~', '2025-04-11 11:00:00');

-- ========== 7. 文章点赞表（已去重） ==========
INSERT INTO likes (user_id, article_id) VALUES
                                            (4, 1), (5, 1), (6, 1),
                                            (4, 2), (5, 2), (6, 2),
                                            (4, 3), (6, 3),
                                            (5, 4), (6, 4), (4, 4),
                                            (6, 5),
                                            (4, 6), (5, 6);

-- ========== 8. 评论点赞表（已去重） ==========
INSERT INTO comment_likes (user_id, comment_id) VALUES
                                                    (5, 1), (6, 1),
                                                    (4, 3), (5, 3),
                                                    (6, 4),
                                                    (5, 7), (6, 7),
                                                    (6, 10);

-- ========== 9. 更新用户积分（评论+2，点赞+1，已去重） ==========
UPDATE user SET point = 14 WHERE user_id = 2; -- author_alex
UPDATE user SET point = 20 WHERE user_id = 3; -- author_maya
