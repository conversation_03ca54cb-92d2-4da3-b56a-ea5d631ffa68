name: CI/CD Pipeline

on:
  push:
    branches:
      - main
      - 'feature/**'
      - 'story/**'
  pull_request:
    branches: [ main ]

permissions:
  contents: read
  checks: write
  pull-requests: write

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'

      - name: <PERSON> execute permission for gradlew
        run: chmod +x gradlew

      - name: Cache Gradle packages
        uses: actions/cache@v4
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      - name: Run tests
        run: ./gradlew test

      - name: Generate test report
        uses: dorny/test-reporter@v1
        if: success() || failure()
        with:
          name: "[Report] JUnit Tests Report"
          path: build/test-results/test/*.xml
          reporter: java-junit
  deploy:
    needs: [ test ]
    runs-on: ubuntu-latest
    environment:
      ${{
      github.ref == 'refs/heads/main' && 'release' ||
      startsWith(github.ref, 'refs/heads/feature/') && 'dev' ||
      startsWith(github.ref, 'refs/heads/story/') && 'dev' ||
      'dev'
      }}
    if: github.ref == 'refs/heads/main' || startsWith(github.ref, 'refs/heads/feature/') || startsWith(github.ref, 'refs/heads/story/')

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'

      - name: Grant execute permission for gradlew
        run: chmod +x gradlew

      - name: Cache Gradle packages
        uses: actions/cache@v4
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      - name: Build application
        run: ./gradlew build -x test

      - name: Install Railway CLI
        if: github.event_name != 'pull_request'
        run: npm install -g @railway/cli

      - name: Deploy to Railway App Service
        if: github.event_name != 'pull_request'
        run: |
          echo "Starting deployment to Railway..."
          railway up --service ${{ secrets.RAILWAY_SERVICE_ID }}
        env:
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
          RAILWAY_PROJECT_ID: ${{ secrets.RAILWAY_PROJECT_ID }}

      - name: Health Check
        run: |
          echo "Starting health check (5 minutes max)..."

          SERVICE_URL="${{ secrets.SERVICE_BASE_URL }}"

          # Wait 60 seconds for service startup
          echo "Waiting 60 seconds for service to start..."
          sleep 60

          # 5-minute health check with 30-second intervals
          for i in {1..10}; do
            echo "Health check attempt $i/10..."
            if curl -s -f --max-time 30 "${SERVICE_URL}/health" > /dev/null 2>&1; then
              echo "✅ Service is healthy!"
              exit 0
            fi
            if [ $i -lt 10 ]; then
              echo "⏳ Service not ready, waiting 30s..."
              sleep 30
            fi
          done
          echo "❌ Health check failed after 5 minutes"
          exit 1