plugins {
    id 'java'
    id 'org.springframework.boot' version '3.5.6'
    id 'io.spring.dependency-management' version '1.1.7'
    id 'jacoco'
}

group = 'org.example'
version = '0.0.1-SNAPSHOT'
description = 'localway-backend'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(17)
    }
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-web'
//    implementation 'org.flywaydb:flyway-core'
//    implementation 'org.flywaydb:flyway-mysql'
    implementation 'org.springframework.boot:spring-boot-starter-jdbc'
    implementation 'com.google.code.gson:gson:2.10.1'
    implementation 'com.aliyun.oss:aliyun-sdk-oss:3.17.4'
    implementation 'jakarta.xml.bind:jakarta.xml.bind-api:4.0.2'
    implementation 'com.sun.activation:jakarta.activation:2.0.1'
    implementation 'org.glassfish.jaxb:jaxb-runtime:4.0.5'
    compileOnly 'org.projectlombok:lombok'
    testRuntimeOnly 'com.h2database:h2'
    runtimeOnly 'com.mysql:mysql-connector-j'
    annotationProcessor 'org.projectlombok:lombok'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
    implementation "org.springframework.ai:spring-ai-starter-model-openai:1.0.2"
    implementation 'org.springframework.ai:spring-ai-starter-mcp-client:1.0.2'
    implementation 'org.springframework.ai:spring-ai-starter-mcp-server-webmvc:1.0.2'
    implementation 'org.springframework.ai:spring-ai-starter-mcp-server-webflux:1.0.2'
}

tasks.named('test') {
    useJUnitPlatform()
}

jacocoTestReport {
    reports {
        xml.required = true
        csv.required = false
        html.required = true
    }
}

test.finalizedBy jacocoTestReport