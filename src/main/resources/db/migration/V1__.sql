CREATE TABLE article
(
    article_id   INT AUTO_INCREMENT      NOT NULL,
    creator_id   INT                     NOT NULL,
    title        VARCHAR(255)            NOT NULL,
    address      TEXT                    NULL,
    content      TEXT                    NULL,
    image        VARCHAR(255)            NULL,
    video        VARCHAR(255)            NULL,
    likes_num    INT                     NOT NULL,
    comments_num INT                     NOT NULL,
    create_time  timestamp DEFAULT NOW() NOT NULL,
    update_time  timestamp DEFAULT NOW() NOT NULL,
    is_deleted   BIT(1)                  NOT NULL,
    CONSTRAINT pk_article PRIMARY KEY (article_id)
);

CREATE TABLE article_tag
(
    id         INT AUTO_INCREMENT NOT NULL,
    article_id INT                NOT NULL,
    tag_id     INT                NOT NULL,
    CONSTRAINT pk_article_tag PRIMARY KEY (id)
);

CREATE TABLE comment
(
    comment_id     INT AUTO_INCREMENT      NOT NULL,
    commentator_id INT                     NOT NULL,
    article_id     INT                     NOT NULL,
    content        TEXT                    NOT NULL,
    create_time    timestamp DEFAULT NOW() NOT NULL,
    CONSTRAINT pk_comment PRIMARY KEY (comment_id)
);

CREATE TABLE comment_likes
(
    id         INT AUTO_INCREMENT NOT NULL,
    user_id    INT                NOT NULL,
    comment_id INT                NOT NULL,
    CONSTRAINT pk_comment_likes PRIMARY KEY (id)
);

CREATE TABLE likes
(
    id         INT AUTO_INCREMENT NOT NULL,
    user_id    INT                NOT NULL,
    article_id INT                NOT NULL,
    CONSTRAINT pk_likes PRIMARY KEY (id)
);

CREATE TABLE reply
(
    reply_id    INT AUTO_INCREMENT      NOT NULL,
    comment_id  INT                     NOT NULL,
    replier_id  INT                     NOT NULL,
    content     TEXT                    NOT NULL,
    create_time timestamp DEFAULT NOW() NOT NULL,
    CONSTRAINT pk_reply PRIMARY KEY (reply_id)
);

CREATE TABLE tag
(
    tag_id   INT AUTO_INCREMENT NOT NULL,
    tag_name VARCHAR(50)        NOT NULL,
    CONSTRAINT pk_tag PRIMARY KEY (tag_id)
);

ALTER TABLE tag
    ADD CONSTRAINT uc_tag_tag_name UNIQUE (tag_name);