server:
  port: 8080
spring:
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:local}
  datasource:
    url: ${DATABASE_URL}
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: ${MYSQL_USERNAME}
    password: ${MY<PERSON>QL_PASSWORD}
  flyway:
    enabled: false
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
    database-platform: org.hibernate.dialect.MySQL8Dialect
  ai:
    openai:
      base-url: https://api.moonshot.cn/
      api-key: 123
      chat:
        options:
          model: moonshot-v1-128k
    mcp:
      client:
        streamable-http:
          connections:
            amap-mcp:
              url: https://mcp.amap.com/mcp?key=${AMAP_KEY}
aliyun:
  oss:
    endPoint: oss-cn-shenzhen.aliyuncs.com
    accessKeyId: ${ALIYUN_ACCESS_KEY_ID:123}
    accessKeySecret: ${ALIYUN_ACCESS_KEY_SECRET:456}
    accessPre: https://localway.oss-cn-shenzhen.aliyuncs.com/
    bucketName: localway