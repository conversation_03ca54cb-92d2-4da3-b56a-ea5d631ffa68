package org.example.localwaybackend.vo;

import lombok.Data;

@Data
public class CommentLikesResponse {
    private String message;
    private Boolean success;

    public CommentLikesResponse(String message, Boolean success) {
        this.message = message;
        this.success = success;
    }

    public static CommentLikesResponse success(String message) {
        return new CommentLikesResponse(message, true);
    }

    public static CommentLikesResponse failure(String message) {
        return new CommentLikesResponse(message, false);
    }
}