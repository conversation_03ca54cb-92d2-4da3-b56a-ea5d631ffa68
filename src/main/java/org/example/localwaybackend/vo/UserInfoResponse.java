package org.example.localwaybackend.vo;

import lombok.Data;

import org.example.localwaybackend.entity.User;

import java.sql.Timestamp;

@Data
public class UserInfoResponse {
    private String userName;
    private String motto;
    private User.Role role;
    private Timestamp createTime;

    // 从User实体转换为响应DTO的静态方法
    public static UserInfoResponse fromUser(User user) {
        UserInfoResponse response = new UserInfoResponse();
        response.setUserName(user.getUserName());
        response.setMotto(user.getMotto());
        response.setRole(user.getRole());
        response.setCreateTime(user.getCreateTime());
        return response;
    }
}