package org.example.localwaybackend.vo;

import lombok.Data;
import org.example.localwaybackend.entity.Article;
import org.example.localwaybackend.entity.Tag;

import java.util.List;

@Data
public class ArticleResponse {
    private Integer articleId;
    private Integer creatorId;
    private String title;
    private String address;
    private String content;
    private String image;
    private String video;
    private Integer likesNum;
    private Integer commentsNum;
    private List<Tag> tagList;
    private Article.Status status;
    private String abstractContent;

}