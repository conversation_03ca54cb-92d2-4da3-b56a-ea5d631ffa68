package org.example.localwaybackend.vo;

import lombok.Data;
import org.example.localwaybackend.entity.Comment;
import org.example.localwaybackend.entity.Reply;

import java.util.List;

@Data
public class CommentWithRepliesResponse {
    // 评论基本信息（可以直接使用Comment实体或复制所需字段）
    private Comment comment;
    // 该评论的所有回复
    private List<Reply> replies;
    // 回复总数
    private int replyCount;

    private Integer likeCount;
}