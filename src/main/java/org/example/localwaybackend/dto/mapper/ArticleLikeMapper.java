package org.example.localwaybackend.dto.mapper;

import org.example.localwaybackend.dto.ArticleLikeRequest;
import org.example.localwaybackend.entity.ArticleLike;
import org.example.localwaybackend.vo.ArticleLikeResponse;
import org.springframework.stereotype.Component;

@Component
public class ArticleLikeMapper {

    public ArticleLike toEntity(ArticleLikeRequest request, Integer articleId) {
        ArticleLike articleLike = new ArticleLike();
        articleLike.setUserId(request.getUserId());
        articleLike.setArticleId(articleId);
        return articleLike;
    }

    public ArticleLikeResponse toResponse(Boolean success, String message) {
        return new ArticleLikeResponse(success, message);
    }
}