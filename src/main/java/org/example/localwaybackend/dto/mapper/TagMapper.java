package org.example.localwaybackend.dto.mapper;

import org.example.localwaybackend.dto.TagRequest;
import org.example.localwaybackend.entity.Tag;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class TagMapper {
    public List<Tag> toEntities(List<TagRequest> tagRequests) {
        if (tagRequests == null) return List.of();

        return tagRequests.stream()
                .filter(Objects::nonNull)
                .map(req -> new Tag(req.getTagName()))
                .collect(Collectors.toList());
    }
}
