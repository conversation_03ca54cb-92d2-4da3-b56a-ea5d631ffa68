package org.example.localwaybackend.dto.mapper;

import org.example.localwaybackend.dto.ArticleRequest;
import org.example.localwaybackend.entity.Article;
import org.example.localwaybackend.vo.ArticleResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ArticleMapper {

    public Article toEntity(ArticleRequest articleRequest) {
        Article article = new Article();
        BeanUtils.copyProperties(articleRequest, article);
        return article;
    }

    public ArticleResponse toResponse(Article article) {
        ArticleResponse response = new ArticleResponse();
        BeanUtils.copyProperties(article, response);
        return response;
    }

    public List<ArticleResponse> toResponses(List<Article> articles) {
        return articles.stream().map(this::toResponse).toList();
    }
}
