package org.example.localwaybackend.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.List;

@Data
public class ArticleRequest {

    @NotNull(message = "creatorId 不能为空")
    private Integer creatorId;

    @NotBlank(message = "标题不能为空")
    @Size(max = 255, message = "标题长度不能超过255")
    private String title;

    private String address;

    @NotBlank(message = "内容不能为空")
    private String content;

    @Size(max = 255, message = "图片路径长度不能超过255")
    private String image;

//    @Size(max = 255, message = "视频路径长度不能超过255")
//    private String video;

    @NotNull(message = "标签列表不能为空")
    @Size(min = 1, max = 10, message = "标签数量必须在1~10个之间")
    private List<@NotNull(message = "标签信息不能为空") TagRequest> tags;
}