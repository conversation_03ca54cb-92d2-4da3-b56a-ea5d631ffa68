package org.example.localwaybackend.async;

import lombok.RequiredArgsConstructor;
import org.example.localwaybackend.entity.Article;
import org.example.localwaybackend.repository.ArticleRepository;
import org.example.localwaybackend.service.TravelPlanService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ArticleSummaryAsyncProcessor {

    private final TravelPlanService travelPlanService;
    private final ArticleRepository articleRepository;

    @Async
    public void generateAndUpdateSummary(Integer articleId) {
        // 调用生成摘要的方法
        String summary = travelPlanService.generateArticleSummary(articleId);
        
        // 获取文章并更新摘要
        Article article = articleRepository.findById(articleId)
                .orElseThrow(() -> new IllegalArgumentException("Article not found with id: " + articleId));
        
        article.setAbstractContent(summary);
        articleRepository.save(article);
    }
}