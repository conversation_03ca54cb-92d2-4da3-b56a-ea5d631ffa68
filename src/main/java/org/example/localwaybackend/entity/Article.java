package org.example.localwaybackend.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Entity
@Table(name = "article")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Article {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "article_id")
    private Integer articleId;

    @Column(name = "creator_id", nullable = false)
    private Integer creatorId;

    @Column(name = "title", nullable = false, length = 255)
    private String title;

    @Column(name = "address", columnDefinition = "TEXT")
    private String address;

    @Column(name = "content", columnDefinition = "TEXT")
    private String content;

    @Column(name = "image", length = 255)
    private String image;

    @Column(name = "video", length = 255)
    private String video;

    @Column(name = "likes_num", nullable = false)
    private Integer likesNum = 0;

    @Column(name = "comments_num", nullable = false)
    private Integer commentsNum = 0;

    @Column(name = "create_time", nullable = false, updatable = false, columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private Timestamp createTime;

    @Column(name = "update_time", nullable = false, columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")
    private Timestamp updateTime;

    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted = false;

    @Column(name = "status", nullable = false)
    @Enumerated(EnumType.STRING)
    private Status status = Status.pending;

    @Column(name = "abstract_content", columnDefinition = "TEXT")
    private String abstractContent;

    public enum Status {
        pending,
        approved,
        rejected
    }
}
