package org.example.localwaybackend.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.time.Instant;

@Data
@Entity
@Table(name = "comment")
@NoArgsConstructor
@AllArgsConstructor
public class Comment {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "comment_id")
    private Integer commentId;

    @Column(name = "commentator_id", nullable = false)
    private Integer commentatorId;

    @Column(name = "article_id", nullable = false)
    private Integer articleId;

    @Column(name = "content", nullable = false, columnDefinition = "TEXT")
    private String content;

    @Column(name = "create_time", nullable = false, updatable = false, columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private Timestamp createTime;

    @PrePersist
    public void setCreateTime() {
        // 如果未设置时间，则使用当前时间（避免JPA显式插入null值）
        if (this.createTime == null) {
            this.createTime = Timestamp.from(Instant.now());
        }
    }
}
