package org.example.localwaybackend.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.sql.Timestamp;
import java.time.Instant;

@Data
@Entity
@Table(name = "user")
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer userId;

    @Column(name = "user_name", length = 50, nullable = false)
    private String userName;

    @Column(name = "password", length = 255, nullable = false)
    private String password;

    @Column(name = "motto", columnDefinition = "TEXT")
    private String motto;

    @Column(name = "role")
    @Enumerated(EnumType.STRING)
    private Role role;

    @Column(name = "create_time", nullable = false, updatable = false, columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private Timestamp createTime;

    @Column(name = "point", nullable = false)
    private Integer point = 0;

    // 枚举类型定义角色
    public enum Role {
        admin, author, user
    }

    @PrePersist
    public void setCreateTime() {
        // 如果未设置时间，则使用当前时间（避免JPA显式插入null值）
        if (this.createTime == null) {
            this.createTime = Timestamp.from(Instant.now());
        }
    }
}