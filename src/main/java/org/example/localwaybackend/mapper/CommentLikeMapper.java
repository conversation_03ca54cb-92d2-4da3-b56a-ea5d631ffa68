package org.example.localwaybackend.mapper;

import org.example.localwaybackend.vo.CommentLikesResponse;
import org.springframework.stereotype.Component;

@Component
public class CommentLikeMapper {

    public CommentLikesResponse toCommentLikesResponse(String result, boolean isSuccess) {
        if (isSuccess) {
            return CommentLikesResponse.success(result);
        } else {
            return CommentLikesResponse.failure(result);
        }
    }
}