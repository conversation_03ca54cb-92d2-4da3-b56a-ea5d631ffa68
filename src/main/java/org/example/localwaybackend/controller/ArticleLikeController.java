package org.example.localwaybackend.controller;


import org.example.localwaybackend.dto.ArticleLikeRequest;
import org.example.localwaybackend.service.ArticleLikeService;
import org.example.localwaybackend.vo.ArticleIdListResponse;
import org.example.localwaybackend.vo.ArticleLikeResponse;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/likes/articles")
public class ArticleLikeController {
    private final ArticleLikeService articleLikeService;

    public ArticleLikeController(ArticleLikeService articleLikeService) {
        this.articleLikeService = articleLikeService;
    }

    @PostMapping("/{articleId}")
    @ResponseStatus(HttpStatus.OK)
    public ArticleLikeResponse toggleLike(@PathVariable Integer articleId, @RequestBody @Validated ArticleLikeRequest request) {
        return articleLikeService.toggleLike(request, articleId);
    }

    @GetMapping("/{userId}")
    @ResponseStatus(HttpStatus.OK)
    public ArticleIdListResponse getLikedArticlesByUserId(@PathVariable Integer userId) {
        ArticleIdListResponse articleIdListResponse = new ArticleIdListResponse();
        List<Integer> articleIdsByUserId = articleLikeService.getArticleIdsByUserId(userId);
        articleIdListResponse.setArticleIds(articleIdsByUserId);
        return articleIdListResponse;
    }
}
