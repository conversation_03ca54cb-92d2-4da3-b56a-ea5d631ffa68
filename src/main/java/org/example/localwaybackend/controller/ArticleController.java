package org.example.localwaybackend.controller;

import org.example.localwaybackend.dto.ArticleRequest;
import org.example.localwaybackend.dto.TagRequest;
import org.example.localwaybackend.dto.mapper.ArticleMapper;
import org.example.localwaybackend.dto.mapper.TagMapper;
import org.example.localwaybackend.entity.Article;
import org.example.localwaybackend.entity.Tag;
import org.example.localwaybackend.service.ArticleService;
import org.example.localwaybackend.service.ArticleTagService;
import org.example.localwaybackend.service.TagService;
import org.example.localwaybackend.vo.ArticleResponse;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/articles")
public class ArticleController {

    private final ArticleService articleService;

    private final TagService tagService;

    private final ArticleTagService articleTagService;

    private final ArticleMapper articleMapper;

    private final TagMapper tagMapper;

    public ArticleController(ArticleService articleService, ArticleMapper articleMapper, TagService tagService, ArticleTagService articleTagService, TagMapper tagMapper) {
        this.articleService = articleService;
        this.articleMapper = articleMapper;
        this.tagService = tagService;
        this.articleTagService = articleTagService;
        this.tagMapper = tagMapper;
    }

    @Transactional
    @PostMapping
    public ArticleResponse createArticle(@RequestBody ArticleRequest articleRequest) {
        List<TagRequest> tags = articleRequest.getTags();
        List<Tag> tagsToAdd = tagService.createTags(tagMapper.toEntities(tags));
        Article article = articleService.createArticle(articleMapper.toEntity(articleRequest));
        articleTagService.createArticleTag(article, tagsToAdd);
        ArticleResponse response = articleMapper.toResponse(article);
        response.setTagList(tagsToAdd);
        return response;
    }

    @GetMapping("/queryPage")
    public List<ArticleResponse> getArticlePage(@RequestParam(required = false) Integer page, @RequestParam(required = false) Integer size, @RequestParam(required = false) String keyWord, @RequestParam(required = false) Boolean author) {
        return articleService.getArticlePage(page, size, keyWord, author);
    }

    @GetMapping("/{creatorId}")
    public List<ArticleResponse> getArticleByCreatorId(@PathVariable Integer creatorId) {
        return articleService.getArticleByCreatorId(creatorId);
    }
}
