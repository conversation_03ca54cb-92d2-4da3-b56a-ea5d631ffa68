package org.example.localwaybackend.controller;

import org.example.localwaybackend.dto.CommentLikeRequest;
import org.example.localwaybackend.entity.CommentLike;
import org.example.localwaybackend.mapper.CommentLikeMapper;
import org.example.localwaybackend.service.CommentLikeService;
import org.example.localwaybackend.vo.CommentIdListResponse;
import org.example.localwaybackend.vo.CommentLikesResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/likes/comments")
public class CommentLikeController {

    public static final String SUCCESS = "点赞成功";
    public static final String CANAL = "取消点赞成功";
    @Autowired
    private CommentLikeService commentLikeService;

    @Autowired
    private CommentLikeMapper commentLikeMapper;

    // 点赞评论
    @PostMapping("/{commentId}")
    @ResponseStatus(HttpStatus.CREATED)
    public CommentLikesResponse likeComment(@PathVariable Integer commentId, @RequestBody CommentLikeRequest commentLikeRequest) {
        CommentLike commentLike = commentLikeService.likeComment(commentId, commentLikeRequest);
        return commentLikeMapper.toCommentLikesResponse(SUCCESS,  commentLike.getId() != null);
    }

    // 取消点赞评论
    @DeleteMapping("/{commentId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public CommentLikesResponse unlikeComment(@PathVariable Integer commentId, @RequestBody CommentLikeRequest commentLikeRequest) {
        commentLikeService.unlikeComment(commentId, commentLikeRequest);
        return commentLikeMapper.toCommentLikesResponse(CANAL, true);
    }

    @GetMapping("/{userId}")
    @ResponseStatus(HttpStatus.OK)
    public CommentIdListResponse getLikedCommentsByUserId(@PathVariable Integer userId) {
        CommentIdListResponse commentIdListResponse = new CommentIdListResponse();
        List<Integer> commentIdsByUserId = commentLikeService.getCommentIdsByUserId(userId);
        commentIdListResponse.setCommentIds(commentIdsByUserId);
        return commentIdListResponse;
    }
}