package org.example.localwaybackend.controller;

import jakarta.validation.Valid;
import org.example.localwaybackend.dto.CommentRequest;
import org.example.localwaybackend.dto.ReplyRequest;
import org.example.localwaybackend.entity.Comment;
import org.example.localwaybackend.service.CommentService;
import org.example.localwaybackend.vo.ArticleCommentsResponse;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/comments")
public class CommentController {

    private final CommentService commentService;

    public CommentController(CommentService commentService) {
        this.commentService = commentService;
    }

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    Comment createComment(@Valid @RequestBody CommentRequest request) {
       return commentService.createComment(request);
    }

    @PostMapping("/reply")
    @ResponseStatus(HttpStatus.CREATED)
    void addReply(@Valid @RequestBody ReplyRequest request) {
       commentService.addReply(request);
    }

    @GetMapping("/{articleId}")
    @ResponseStatus(HttpStatus.OK)
    ArticleCommentsResponse getCommentAndReplies(@PathVariable Integer articleId) {
        return commentService.getCommentsWithReplies(articleId);
    }
}
