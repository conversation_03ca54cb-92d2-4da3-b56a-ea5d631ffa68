package org.example.localwaybackend.controller;

import org.example.localwaybackend.entity.User;
import org.example.localwaybackend.service.UserService;
import org.example.localwaybackend.vo.UserInfoResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/users")
public class UserController {

    private final UserService userService;

    public UserController(UserService userService) {
        this.userService = userService;
    }

    @GetMapping("/{userId}")
    UserInfoResponse getUserInfoById(@PathVariable Integer userId) {
        User user = userService.getUserInfoById(userId);
        return UserInfoResponse.fromUser(user);
    }

    @GetMapping("/likes/{userId}")
    public Integer getUserLikesById(@PathVariable Integer userId) {
        return userService.getUserLikesById(userId);
    }
}
