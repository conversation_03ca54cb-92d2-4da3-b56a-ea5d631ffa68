package org.example.localwaybackend.controller;

import org.example.localwaybackend.util.OssUtil;
import org.example.localwaybackend.vo.ImageResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * OSS 文件控制器
 */
@RestController
@RequestMapping("/oss")
public class OssController {
    @Autowired
    private OssUtil ossUtil;

    @PostMapping("/uploadImg")
    public ImageResponse upload(MultipartFile file) {
        String url = ossUtil.uploadMultipartFile(file);
        ImageResponse imageResponse = new ImageResponse();
        imageResponse.setUrl(url);
        return imageResponse;
    }
}