package org.example.localwaybackend.repository;

import jakarta.validation.constraints.NotNull;
import org.example.localwaybackend.entity.Comment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CommentRepository extends JpaRepository<Comment, Integer> {
    @Query("SELECT c FROM Comment c WHERE c.articleId = :articleId ORDER BY c.createTime DESC")
    List<Comment> findByArticleIdOrderByCreateTime(@Param("articleId") Integer articleId);

    Comment findCommentByCommentId(@NotNull(message = "评论ID不能为空") Integer commentId);
}
    