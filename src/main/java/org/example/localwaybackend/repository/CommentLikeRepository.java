package org.example.localwaybackend.repository;

import org.example.localwaybackend.entity.CommentLike;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CommentLikeRepository extends JpaRepository<CommentLike, Integer> {
    Optional<CommentLike> findByUserIdAndCommentId(Integer userId, Integer commentId);
    List<CommentLike> findByCommentId(Integer commentId);

    @Query("SELECT cl.commentId FROM CommentLike cl WHERE cl.userId = :userId")
    List<Integer> findCommentIdsByUserId(Integer userId);
}
