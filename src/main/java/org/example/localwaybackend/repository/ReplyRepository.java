package org.example.localwaybackend.repository;

import org.example.localwaybackend.entity.Reply;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ReplyRepository extends JpaRepository<Reply, Integer> {

    @Query("SELECT r FROM Reply r WHERE r.commentId = :commentId ORDER BY r.createTime DESC")
    List<Reply> findByCommentIdOrderByCreateTime(@Param("commentId") Integer commentId);
}
