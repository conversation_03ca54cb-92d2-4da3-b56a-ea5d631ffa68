package org.example.localwaybackend.repository;

import org.example.localwaybackend.entity.ArticleLike;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ArticleLikeRepository extends JpaRepository<ArticleLike, Integer> {
    boolean existsByUserIdAndArticleId(Integer userId, Integer articleId);
    void deleteByUserIdAndArticleId(Integer userId, Integer articleId);

    @Query("SELECT al.articleId FROM ArticleLike al WHERE al.userId = :userId")
    List<Integer> findArticleIdsByUserId(@Param("userId") Integer userId);
}