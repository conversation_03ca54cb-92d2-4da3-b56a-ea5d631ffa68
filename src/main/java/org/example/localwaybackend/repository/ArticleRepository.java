package org.example.localwaybackend.repository;

import org.example.localwaybackend.entity.Article;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ArticleRepository extends JpaRepository<Article, Integer> {

    @Query("SELECT a FROM Article a WHERE a.isDeleted = false ORDER BY a.likesNum DESC")
    Page<Article> findAllNotDeleted(Pageable pageable);

    @Query("SELECT a FROM Article a WHERE a.isDeleted = false AND (a.title LIKE %:keyword% OR a.content LIKE %:keyword% OR a.address LIKE %:keyword%) ORDER BY a.likesNum DESC")
    Page<Article> findByKeywordInTitleOrContent(@Param("keyword") String keyword, Pageable pageable);

    Article findArticleByArticleId(Integer articleId);

    List<Article> findArticleByCreatorIdOrderByLikesNumDesc(Integer creatorId);

    @Query("SELECT a FROM Article a JOIN User u ON a.creatorId = u.userId WHERE a.isDeleted = false AND u.role = 'author' ORDER BY a.likesNum DESC")
    Page<Article> findAllNotDeletedByAuthorRole(Pageable pageable);

    @Query("SELECT a FROM Article a JOIN User u ON a.creatorId = u.userId WHERE a.isDeleted = false AND (a.title LIKE %:keyword% OR a.content LIKE %:keyword% OR a.address LIKE %:keyword%) AND u.role = 'author' ORDER BY a.likesNum DESC")
    Page<Article> findByKeywordInTitleOrContentAndAuthorRole(String trim, Pageable pageable);

    @Query("SELECT a.abstractContent FROM Article a WHERE a.isDeleted = false AND a.address = :address")
    List<String> findAbstractContentsByAddress(@Param("address") String address);
}
