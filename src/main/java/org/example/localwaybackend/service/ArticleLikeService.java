package org.example.localwaybackend.service;

import org.example.localwaybackend.dto.ArticleLikeRequest;
import org.example.localwaybackend.dto.mapper.ArticleLikeMapper;
import org.example.localwaybackend.entity.Article;
import org.example.localwaybackend.entity.ArticleLike;
import org.example.localwaybackend.repository.ArticleLikeRepository;
import org.example.localwaybackend.repository.ArticleRepository;
import org.example.localwaybackend.repository.UserRepository;
import org.example.localwaybackend.vo.ArticleLikeResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class ArticleLikeService {

    private final ArticleLikeRepository articleLikeRepository;
    private final ArticleLikeMapper articleLikeMapper;
    private final ArticleRepository articleRepository;
    private final UserRepository userRepository;

    @Autowired
    public ArticleLikeService(ArticleLikeRepository articleLikeRepository,
                              ArticleLikeMapper articleLikeMapper,
                              ArticleRepository articleRepository,
                              UserRepository userRepository) {
        this.userRepository = userRepository;
        this.articleRepository = articleRepository;
        this.articleLikeRepository = articleLikeRepository;
        this.articleLikeMapper = articleLikeMapper;
    }

    private boolean checkIfLiked(Integer userId, Integer articleId) {
        return articleLikeRepository.existsByUserIdAndArticleId(userId, articleId);
    }

    @Transactional
    public ArticleLikeResponse toggleLike(ArticleLikeRequest request, Integer articleId) {
        Integer userId = request.getUserId();
        if (checkIfLiked(userId, articleId)) {
            return unlikeArticle(userId, articleId);
        } else {
            return likeArticle(request, articleId);
        }
    }

    private ArticleLikeResponse likeArticle(ArticleLikeRequest request, Integer articleId) {
        ArticleLike articleLike = articleLikeMapper.toEntity(request, articleId);
        articleLikeRepository.save(articleLike);
        Article articleByArticleId = articleRepository.findArticleByArticleId(articleId);
        articleByArticleId.setLikesNum(articleByArticleId.getLikesNum() + 1);
        userRepository.findById(articleByArticleId.getCreatorId()).ifPresent(user -> {
            user.setPoint(user.getPoint() + 1);
            userRepository.save(user);
        });
        articleRepository.save(articleByArticleId);
        return articleLikeMapper.toResponse(true, "Article liked successfully");
    }

    private ArticleLikeResponse unlikeArticle(Integer userId, Integer articleId) {
        articleLikeRepository.deleteByUserIdAndArticleId(userId, articleId);
        Article articleByArticleId = articleRepository.findArticleByArticleId(articleId);
        articleByArticleId.setLikesNum(articleByArticleId.getLikesNum() - 1);
        userRepository.findById(articleByArticleId.getCreatorId()).ifPresent(user -> {
            user.setPoint(user.getPoint() - 1);
            userRepository.save(user);
        });
        articleRepository.save(articleByArticleId);
        return articleLikeMapper.toResponse(true, "Article unliked successfully");
    }

    public List<Integer> getArticleIdsByUserId(Integer userId) {
        return articleLikeRepository.findArticleIdsByUserId(userId);
    }
}