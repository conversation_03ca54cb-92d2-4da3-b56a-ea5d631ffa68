package org.example.localwaybackend.service;

import org.example.localwaybackend.repository.ArticleRepository;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AbstractContentService {

    final
    ArticleRepository articleRepository;

    public AbstractContentService(ArticleRepository articleRepository) {
        this.articleRepository = articleRepository;
    }

    // 根据地址查询所有未删除文章的摘要
    public List<String> getAbstractContentsByAddress(String address) {
        return articleRepository.findAbstractContentsByAddress(address);
    }
}
