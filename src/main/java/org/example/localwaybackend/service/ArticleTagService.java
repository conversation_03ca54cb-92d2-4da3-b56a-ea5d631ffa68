package org.example.localwaybackend.service;

import org.example.localwaybackend.entity.Article;
import org.example.localwaybackend.entity.ArticleTag;
import org.example.localwaybackend.entity.Tag;
import org.example.localwaybackend.repository.ArticleTagRepository;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class ArticleTagService {

    private final ArticleTagRepository articleTagRepository;

    public ArticleTagService(ArticleTagRepository articleTagRepository) {
        this.articleTagRepository = articleTagRepository;
    }

    public List<ArticleTag> createArticleTag(Article article, List<Tag> tags) {
        List<ArticleTag> articleTags = new ArrayList<>();
        for (Tag tag : tags) {
            ArticleTag articleTag = new ArticleTag();
            articleTag.setArticleId(article.getArticleId());
            articleTag.setTagId(tag.getTagId());
            articleTags.add(articleTag);
            articleTagRepository.save(articleTag);
        }
        return articleTags;
    }
}
