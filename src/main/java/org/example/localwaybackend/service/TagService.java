package org.example.localwaybackend.service;

import org.example.localwaybackend.entity.Tag;
import org.example.localwaybackend.repository.TagRepository;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class TagService {

    private final TagRepository tagRepository;

    public TagService(TagRepository tagRepository) {
        this.tagRepository = tagRepository;
    }

    public List<Tag> createTags(List<Tag> tags) {

        List<Tag> result = new ArrayList<>();

        for (Tag tag : tags) {
            Tag existingTag = tagRepository.findByTagName(tag.getTagName());
            if (existingTag != null) {
                result.add(existingTag);
            } else {
                Tag savedTag = tagRepository.save(tag);
                result.add(savedTag);
            }
        }

        return result;
    }
}
