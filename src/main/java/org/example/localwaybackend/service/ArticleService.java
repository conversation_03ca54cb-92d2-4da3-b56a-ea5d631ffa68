package org.example.localwaybackend.service;

import org.example.localwaybackend.async.ArticleSummaryAsyncProcessor;
import org.example.localwaybackend.dto.mapper.ArticleMapper;
import org.example.localwaybackend.entity.Article;
import org.example.localwaybackend.entity.ArticleTag;
import org.example.localwaybackend.entity.Tag;
import org.example.localwaybackend.repository.ArticleRepository;
import org.example.localwaybackend.repository.ArticleTagRepository;
import org.example.localwaybackend.repository.TagRepository;
import org.example.localwaybackend.vo.ArticleResponse;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class ArticleService {
    private final ArticleRepository articleRepository;

    private final TagRepository tagRepository;

    private final ArticleTagRepository articleTagRepository;

    private final ArticleMapper articleMapper;

    private final ArticleSummaryAsyncProcessor summaryAsyncProcessor;

    public ArticleService(ArticleRepository articleRepository, TagRepository tagRepository, ArticleTagRepository articleTagRepository, ArticleMapper articleMapper, ArticleSummaryAsyncProcessor summaryAsyncProcessor) {
        this.articleRepository = articleRepository;
        this.tagRepository = tagRepository;
        this.articleTagRepository = articleTagRepository;
        this.articleMapper = articleMapper;
        this.summaryAsyncProcessor = summaryAsyncProcessor;
    }

    public Article createArticle(Article article) {
        Article savedArticle = articleRepository.save(article);
        // 异步生成并更新摘要，任务失败会直接抛出异常
        summaryAsyncProcessor.generateAndUpdateSummary(savedArticle.getArticleId());
        return savedArticle;
    }

    public List<ArticleResponse> getArticlePage(Integer page, Integer size, String keyWord, Boolean author) {
        Pageable pageable = PageRequest.of(page - 1, size);

        List<Article> articleList;
        if (author != null && author) {
            if (keyWord == null || keyWord.trim().isEmpty()) {
                articleList = articleRepository.findAllNotDeletedByAuthorRole(pageable).stream().toList();
            } else {
                articleList = articleRepository.findByKeywordInTitleOrContentAndAuthorRole(keyWord.trim(), pageable).stream().toList();
            }
        } else {
            if (keyWord == null || keyWord.trim().isEmpty()) {
                articleList = articleRepository.findAllNotDeleted(pageable).stream().toList();
            } else {
                articleList = articleRepository.findByKeywordInTitleOrContent(keyWord.trim(), pageable).stream().toList();
            }
        }
        return convertToResponses(articleList);
    }

    private List<ArticleResponse> convertToResponses(List<Article> articles) {
        List<ArticleResponse> responses = new ArrayList<>();
        for (Article article : articles) {
            List<ArticleTag> articleTags = articleTagRepository.findArticleTagByArticleId(article.getArticleId());
            List<Integer> tagIds = articleTags.stream()
                    .map(ArticleTag::getTagId)
                    .toList();
            List<Tag> tags = tagRepository.findAllById(tagIds);
            ArticleResponse response = articleMapper.toResponse(article);
            response.setTagList(tags);
            responses.add(response);
        }
        return responses;
    }

    public List<ArticleResponse> getArticleByCreatorId(Integer creatorId) {
        List<Article> articleList = articleRepository.findArticleByCreatorIdOrderByLikesNumDesc(creatorId);
        return articleMapper.toResponses(articleList);
    }
}
