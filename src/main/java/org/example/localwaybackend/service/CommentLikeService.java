package org.example.localwaybackend.service;

import org.example.localwaybackend.dto.CommentLikeRequest;
import org.example.localwaybackend.entity.CommentLike;
import org.example.localwaybackend.exception.CommentLikeEmptyException;
import org.example.localwaybackend.exception.CommentLikeExistsException;
import org.example.localwaybackend.repository.CommentLikeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class CommentLikeService {

    @Autowired
    private CommentLikeRepository commentLikeRepository;

    /**
     * 为评论点赞
     * @return 点赞记录
     */
    public CommentLike likeComment(Integer commentId, CommentLikeRequest commentLikeRequest) {
        Integer userId = commentLikeRequest.getUserId();
        // 检查是否已经点赞
        Optional<CommentLike> existingLike = commentLikeRepository.findByUserIdAndCommentId(userId, commentId);
        if (existingLike.isPresent()) {
            throw new CommentLikeExistsException("已经点赞过该评论");
        }
        CommentLike commentLike = new CommentLike();
        commentLike.setUserId(userId);
        commentLike.setCommentId(commentId);
        return commentLikeRepository.save(commentLike);
    }

    /**
     * 取消评论点赞
     *
     */
    public void unlikeComment(Integer commentId, CommentLikeRequest commentLikeRequest) {
        Integer userId = commentLikeRequest.getUserId();
        Optional<CommentLike> existingLike = commentLikeRepository.findByUserIdAndCommentId(userId, commentId);
        if (existingLike.isEmpty()) {
            throw new CommentLikeEmptyException("未找到点赞记录");
        }
        commentLikeRepository.delete(existingLike.get());
    }
    
    /**
     * 获取评论的点赞数
     *
     * @param commentId 评论ID
     * @return 点赞数
     */
    public Integer getCommentLikeCount(Integer commentId) {
        return commentLikeRepository.findByCommentId(commentId).size();
    }


    public List<Integer> getCommentIdsByUserId(Integer userId) {
        return commentLikeRepository.findCommentIdsByUserId(userId);
    }
}