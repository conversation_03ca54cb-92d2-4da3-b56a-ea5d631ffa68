package org.example.localwaybackend.service;

import org.example.localwaybackend.entity.Article;
import org.example.localwaybackend.repository.ArticleRepository;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Optional;

@Service
public class TravelPlanService {

    private final ArticleRepository articleRepository;
    private final ChatClient chatClient;

    public TravelPlanService(ArticleRepository articleRepository, ChatClient.Builder chatClientBuilder) {
        this.articleRepository = articleRepository;
        this.chatClient = chatClientBuilder.build();
    }

    public String generateArticleSummary(Integer articleId) {
        Optional<Article> articleOptional = articleRepository.findById(articleId);
        if (articleOptional.isEmpty()) {
            throw new IllegalArgumentException("Article with ID " + articleId + " not found.");
        }
        Article article = articleOptional.get();
        String articleContent = buildArticleContent(article);
        String promptTemplate = loadPromptTemplate();
        return chatClient.prompt()
                .system(promptTemplate)
                .user(articleContent)
                .call()
                .content();
    }
    
    private String buildArticleContent(Article article) {
        StringBuilder content = new StringBuilder();
        content.append("标题: ").append(article.getTitle()).append("\n");
        if (article.getAddress() != null && !article.getAddress().isEmpty()) {
            content.append("地址: ").append(article.getAddress()).append("\n");
        }
        if (article.getContent() != null && !article.getContent().isEmpty()) {
            content.append("内容: ").append(article.getContent()).append("\n");
        }
        return content.toString();
    }
    
    private String loadPromptTemplate() {
        try {
            ClassPathResource resource = new ClassPathResource("agent/article_summary_prompt.txt");
            if (resource.exists()) {
                return new String(resource.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
            }

            String currentDir = System.getProperty("user.dir");
            String filePath = currentDir + "/src/main/resources/agent/article_summary_prompt.txt";
            return Files.readString(Paths.get(filePath), StandardCharsets.UTF_8);
            
        } catch (IOException e) {
            System.err.println("Warning: Could not load article summary prompt from file. Using default prompt. Error: " + e.getMessage());
            return getDefaultPrompt();
        }
    }
    
    private String getDefaultPrompt() {
        return """
                你是一个专业的旅游文章摘要生成器。你的任务是根据提供的旅游文章内容，生成一个简洁明了的摘要。
                摘要应该包括以下要素：
                1. 旅游目的地
                2. 主要的旅游体验或活动
                3. 特色美食或文化
                4. 整体感受或推荐理由
                
                请用中文回复，摘要长度控制在100-200字之间。
                """;
    }
}