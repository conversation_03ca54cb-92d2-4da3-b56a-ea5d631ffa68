package org.example.localwaybackend.service;

import org.example.localwaybackend.dto.CommentRequest;
import org.example.localwaybackend.dto.ReplyRequest;
import org.example.localwaybackend.entity.Article;
import org.example.localwaybackend.entity.Comment;
import org.example.localwaybackend.entity.Reply;
import org.example.localwaybackend.repository.*;
import org.example.localwaybackend.vo.ArticleCommentsResponse;
import org.example.localwaybackend.vo.CommentWithRepliesResponse;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class CommentService {

    private final CommentRepository commentRepository;
    private final ReplyRepository replyRepository;
    private final CommentLikeRepository commentLikeRepository;
    private final ArticleRepository articleRepository;
    private final UserRepository userRepository;


    public CommentService(CommentRepository commentRepository,
                          ReplyRepository replyRepository,
                          CommentLikeRepository commentLikeRepository,
                          ArticleRepository articleRepository,
                          UserRepository userRepository) {
        this.userRepository = userRepository;
        this.commentRepository = commentRepository;
        this.replyRepository = replyRepository;
        this.commentLikeRepository = commentLikeRepository;
        this.articleRepository = articleRepository;
    }

    public Comment createComment(CommentRequest request) {
        Comment comment = new Comment();
        comment.setCommentatorId(request.getCommentatorId());
        comment.setArticleId(request.getArticleId());
        comment.setContent(request.getContent());
        Article articleByArticleId = articleRepository.findArticleByArticleId(request.getArticleId());
        articleByArticleId.setCommentsNum(articleByArticleId.getCommentsNum() + 1);
        userRepository.findById(articleByArticleId.getCreatorId()).ifPresent(user -> {
            user.setPoint(user.getPoint() + 2);
            userRepository.save(user);
        });
        articleRepository.save(articleByArticleId);
        return commentRepository.save(comment);
    }

    public void addReply(ReplyRequest request) {
        Reply reply = new Reply();
        reply.setCommentId(request.getCommentId());
        reply.setReplierId(request.getReplierId());
        reply.setContent(request.getContent());
        Comment comment = commentRepository.findCommentByCommentId(request.getCommentId());
        Article articleByArticleId = articleRepository.findArticleByArticleId(comment.getArticleId());
        articleByArticleId.setCommentsNum(articleByArticleId.getCommentsNum() + 1);
        articleRepository.save(articleByArticleId);
        replyRepository.save(reply);
    }


    public ArticleCommentsResponse getCommentsWithReplies(Integer articleId) {
        // 1. 查询该文章的所有评论
        List<Comment> comments = commentRepository.findByArticleIdOrderByCreateTime(articleId);

        // 2. 为每个评论查询对应的回复，并封装成 CommentWithRepliesDTO
        List<CommentWithRepliesResponse> commentWithReplies = comments.stream()
                .map(comment -> {
                    CommentWithRepliesResponse dto = new CommentWithRepliesResponse();
                    dto.setLikeCount(commentLikeRepository.findByCommentId(comment.getCommentId()).size());
                    dto.setComment(comment);
                    // 查询当前评论的所有回复
                    List<Reply> replies = replyRepository.findByCommentIdOrderByCreateTime(comment.getCommentId());
                    dto.setReplies(replies);
                    dto.setReplyCount(replies.size()); // 设置回复数量
                    return dto;
                })
                .collect(Collectors.toList());
        // 3. 组装最终响应
        ArticleCommentsResponse response = new ArticleCommentsResponse();
        response.setArticleId(articleId);
        response.setCommentCount(comments.size()); // 设置评论总数
        response.setComments(commentWithReplies);
        return response;
    }
}

