package org.example.localwaybackend.service;

import org.example.localwaybackend.entity.User;
import org.example.localwaybackend.exception.UserNotExistsException;
import org.example.localwaybackend.repository.UserRepository;
import org.example.localwaybackend.vo.ArticleResponse;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UserService {
    private final UserRepository userRepository;

    private final ArticleService articleService;

    public UserService(UserRepository userRepository, ArticleService articleService) {
        this.userRepository = userRepository;
        this.articleService = articleService;
    }

    public User getUserInfoById(Integer userId) {
        if (userRepository.findById(userId).isPresent())
            return userRepository.findById(userId).get();
        else {
            throw new UserNotExistsException("User with ID " + userId + " does not exist.");
        }
    }

    public Integer getUserLikesById(Integer userId) {
        List<ArticleResponse> articleByCreatorId = articleService.getArticleByCreatorId(userId);
        return articleByCreatorId.stream().mapToInt(ArticleResponse::getLikesNum).sum();
    }
}
