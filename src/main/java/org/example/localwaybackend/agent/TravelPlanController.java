package org.example.localwaybackend.agent;

import org.example.localwaybackend.service.TravelPlanService;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Map;
import java.util.Random;

@RestController
public class TravelPlanController {

    private final ChatClient chatClient;
    private final ToolCallbackProvider mcpToolProvider;
    private final String systemPrompt;
    private final TravelPlanService travelPlanService;

    public TravelPlanController(ChatClient.Builder chatClientBuilder, 
                               ToolCallbackProvider mcpToolProvider,
                               TravelPlanService travelPlanService) {
        this.chatClient = chatClientBuilder.build();
        this.mcpToolProvider = mcpToolProvider;
        this.systemPrompt = loadSystemPrompt();
        this.travelPlanService = travelPlanService;
    }

    private String loadSystemPrompt() {
        try {
            // Try to load from classpath first (resources folder)
            ClassPathResource resource = new ClassPathResource("agent/system_prompt.txt");
            if (resource.exists()) {
                return new String(resource.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
            }
            
            // Fallback to file system path
            String currentDir = System.getProperty("user.dir");
            String filePath = currentDir + "/src/main/java/org/example/localwaybackend/agent/system_prompt.txt";
            return Files.readString(Paths.get(filePath), StandardCharsets.UTF_8);
            
        } catch (IOException e) {
            // Fallback to default prompt if file loading fails
            System.err.println("Warning: Could not load system prompt from file. Using default prompt. Error: " + e.getMessage());
            return getDefaultSystemPrompt();
        }
    }

    private String getDefaultSystemPrompt() {
        return """
                角色設置：
                你是一位專注於深度旅遊的行程規劃專家，擅長打造以地道文化體驗為核心的旅遊路線。你的角色是設計一個智能旅遊指南平台，該平台結合高德MCP地圖數據分析、用戶偏好解讀和本地文化達人的推薦，為用戶生成精準且個性化的行程規劃。
                平台的目標是幫助旅客發掘當地隱藏的文化瑰寶，而非擁擠的網紅地標，提供充分融入當地生活的旅遊體驗。
                現在，請根據用戶需求提供旅遊行程建議！
                """;
    }

    @GetMapping("/chat")
    String routePlanChat(@RequestParam String userInput) {
        return this.chatClient.prompt()
                .system(systemPrompt)
                .user(userInput)
                .toolContext(Map.of("progressToken", "token-" + new Random().nextInt()))
                .toolCallbacks(mcpToolProvider)
                .call()
                .content();
    }

    @GetMapping(value = "/chat/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    Flux<String> streamRoutePlanChat(@RequestParam String userInput) {
        return this.chatClient.prompt()
                .system(systemPrompt)
                .user(userInput)
                .toolContext(Map.of("progressToken", "token-" + new Random().nextInt()))
                .toolCallbacks(mcpToolProvider)
                .stream()
                .content();
    }
    
    @GetMapping("/chat/{articleId}/summary")
    public String generateArticleSummary(@PathVariable Integer articleId) {
        return travelPlanService.generateArticleSummary(articleId);
    }
}