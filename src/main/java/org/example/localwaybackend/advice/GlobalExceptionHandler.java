package org.example.localwaybackend.advice;

import org.example.localwaybackend.exception.CommentLikeExistsException;
import org.example.localwaybackend.exception.CommentLikeEmptyException;
import org.example.localwaybackend.exception.UserNotExistsException;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.stream.Collectors;

@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
    public ErrorResponse handleMethodArgumentNotValid(MethodArgumentNotValidException e) {
        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(DefaultMessageSourceResolvable::getDefaultMessage)
                .collect(Collectors.joining("; "));

        // 返回包含错误码和拼接后信息的响应
        return new ErrorResponse(422, errorMessage);
    }

    @ExceptionHandler(CommentLikeExistsException.class)
    @ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
    public ResponseException handleCommentLikeException(CommentLikeExistsException ex) {
        return new ResponseException(ex.getMessage());
    }

    @ExceptionHandler(CommentLikeEmptyException.class)
    @ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
    public ResponseException handleEmptyException(CommentLikeEmptyException ex) {
        return new ResponseException(ex.getMessage());
    }


    @ExceptionHandler(UserNotExistsException.class)
    @ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
    public ResponseException handleEmptyException(UserNotExistsException ex) {
        return new ResponseException(ex.getMessage());
    }

}
