package org.example.localwaybackend;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@SpringBootApplication
@RestController
@EnableAsync
public class LocalwayBackendApplication {

    public static void main(String[] args) {
        SpringApplication.run(LocalwayBackendApplication.class, args);
    }

    @GetMapping("/health")
    public String healthy(){
        return "I'm healthy";
    }
}
