spring:
    datasource:
        # Use H2 in-memory database for testing
        url: jdbc:h2:mem:testdb;MODE=MySQL;DATABASE_TO_LOWER=TRUE;DB_CLOSE_DELAY=-1
        username: sa
        password:
        driver-class-name: org.h2.Driver
    jpa:
        show-sql: true
        hibernate:
            ddl-auto: update
    ai:
      openai:
        base-url: https://api.moonshot.cn/
        api-key: 123
        chat:
          options:
            model: moonshot-v1-128k
aliyun:
    oss:
        endPoint: oss-cn-shenzhen.aliyuncs.com
        accessKeyId: 123
        accessKeySecret: 456
        accessPre: https://localway.oss-cn-shenzhen.aliyuncs.com/
        bucketName: localway