package org.example.localwaybackend;

import org.example.localwaybackend.repository.ArticleLikeRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@AutoConfigureMockMvc
public class ArticleLikeControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ArticleLikeRepository articleLikeRepository;

    @BeforeEach
    void setUp() {
        articleLikeRepository.deleteAll();
    }

    @Test
    public void should_return_successful_message_when_like_article() throws Exception {
        assertFalse(articleLikeRepository.existsByUserIdAndArticleId(101, 1));

        String postBody = """
                {
                    "userId": 101
                }
                """;

        MockHttpServletRequestBuilder request = post("/likes/articles/1")
                .contentType(MediaType.APPLICATION_JSON)
                .content(postBody);

        mockMvc.perform(request)
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Article liked successfully"));
    }

    @Test
    public void should_toggle_like_when_article_already_liked() throws Exception {
        String postBody = """
                {
                    "userId": 101
                }
                """;

        MockHttpServletRequestBuilder request = post("/likes/articles/1")
                .contentType(MediaType.APPLICATION_JSON)
                .content(postBody);

        mockMvc.perform(request)
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Article liked successfully"));

        assertTrue(articleLikeRepository.existsByUserIdAndArticleId(101, 1));

        mockMvc.perform(request)
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Article unliked successfully"));

        assertFalse(articleLikeRepository.existsByUserIdAndArticleId(101, 1));
    }
}