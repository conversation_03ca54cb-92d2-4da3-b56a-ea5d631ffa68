package org.example.localwaybackend;

import org.example.localwaybackend.entity.CommentLike;
import org.example.localwaybackend.repository.CommentLikeRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@AutoConfigureMockMvc
public class CommentLikeControllerTests {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private CommentLikeRepository commentLikeRepository;

    @BeforeEach
    public void setup() {
        commentLikeRepository.deleteAll();
    }

    @Test
    void should_like_comment_successfully() throws Exception {
        // Given: 用户位于发布内容详情页的评论区域
        Integer commentId = 1;

        // When: 点击评论下的"喜欢"按钮
        MockHttpServletRequestBuilder request = post("/likes/comments/{commentId}", commentId)
                .contentType(MediaType.APPLICATION_JSON);

        mockMvc.perform(request)
                // Then: 图标从轮廓变为填充，点赞数增加 1
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("点赞成功"));
    }

    @Test
    void should_return_error_when_like_comment_again() throws Exception {
        // Given: 用户已经点赞了某条评论
        Integer commentId = 1;
        Integer userId = 1;
        
        // 先创建一个点赞记录
        CommentLike commentLike = new CommentLike();
        commentLike.setUserId(userId);
        commentLike.setCommentId(commentId);
        commentLikeRepository.save(commentLike);

        // When: 再次点击评论下的"喜欢"按钮
        MockHttpServletRequestBuilder request = post("/likes/comments/{commentId}", commentId)
                .contentType(MediaType.APPLICATION_JSON);

        mockMvc.perform(request)
                // Then: 返回错误信息
                .andExpect(status().isUnprocessableEntity())
                .andExpect(jsonPath("$.message").value("已经点赞过该评论"));
    }

    @Test
    void should_return_error_when_try_to_unlike_not_liked_comment() throws Exception {
        // Given: 用户尝试取消点赞一个未点赞的评论
        Integer commentId = 1;

        // When: 点击取消点赞按钮
        MockHttpServletRequestBuilder request = delete("/likes/comments/{commentId}", commentId)
                .contentType(MediaType.APPLICATION_JSON);

        mockMvc.perform(request)
                // Then: 返回错误信息
                .andExpect(status().isUnprocessableEntity())
                .andExpect(jsonPath("$.message").value("未找到点赞记录"));
    }

    @Test
    void should_unlike_comment_successfully() throws Exception {
        // Given: 用户已经点赞了某条评论
        Integer commentId = 1;
        Integer userId = 1;
        
        // 先创建一个点赞记录
        CommentLike commentLike = new CommentLike();
        commentLike.setUserId(userId);
        commentLike.setCommentId(commentId);
        commentLikeRepository.save(commentLike);

        // When: 点击取消点赞按钮
        MockHttpServletRequestBuilder request = delete("/likes/comments/{commentId}", commentId)
                .contentType(MediaType.APPLICATION_JSON);

        mockMvc.perform(request)
                // Then: 成功取消点赞
                .andExpect(status().isNoContent());
    }
}